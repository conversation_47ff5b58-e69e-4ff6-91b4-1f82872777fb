import { UserSubscriptionService } from "./user-subscription.service"
import { PerkService } from "../perk/perk.service"
import { SUBSCRIPTION_LIMITS } from "./user-subscription.types"
import { AI_USAGE_LIMITS } from "../user-ai-usage/user-ai-usage.types"

/**
 * Enhanced subscription service that considers both subscription status and active perks
 */
export class PerkAwareSubscriptionService {
  /**
   * Get enhanced user limits considering both subscription and perks
   */
  static async getEnhancedUserLimits(userId: string) {
    try {
      // Get base subscription limits
      const subscription = await UserSubscriptionService.getUserSubscription(userId)
      const isSubscribed = 
        subscription?.subscriptionStatus === "active" || 
        subscription?.subscriptionStatus === "trialing"

      // Get base limits from subscription
      const baseLimits = {
        maxSquads: isSubscribed 
          ? SUBSCRIPTION_LIMITS.PRO.MAX_SQUADS 
          : SUBSCRIPTION_LIMITS.FREE.MAX_SQUADS,
        maxTripsPerSquad: isSubscribed 
          ? SUBSCRIPTION_LIMITS.PRO.MAX_TRIPS_PER_SQUAD 
          : SUBSCRIPTION_LIMITS.FREE.MAX_TRIPS_PER_SQUAD,
        maxDailyAIRequests: isSubscribed 
          ? AI_USAGE_LIMITS.PRO.DAILY 
          : AI_USAGE_LIMITS.FREE.DAILY,
        maxWeeklyAIRequests: isSubscribed 
          ? AI_USAGE_LIMITS.PRO.WEEKLY 
          : AI_USAGE_LIMITS.FREE.WEEKLY,
        hasTripChat: isSubscribed 
          ? SUBSCRIPTION_LIMITS.PRO.HAS_TRIP_CHAT 
          : SUBSCRIPTION_LIMITS.FREE.HAS_TRIP_CHAT,
      }

      // Get perk enhancements
      const perkSummary = await PerkService.calculateUserPerkSummary(userId)

      // Apply perk enhancements to base limits
      const enhancedLimits = {
        maxSquads: baseLimits.maxSquads + perkSummary.additionalSquads,
        maxTripsPerSquad: baseLimits.maxTripsPerSquad + perkSummary.additionalTrips,
        maxDailyAIRequests: baseLimits.maxDailyAIRequests + perkSummary.additionalDailyAI,
        maxWeeklyAIRequests: baseLimits.maxWeeklyAIRequests + perkSummary.additionalWeeklyAI,
        hasTripChat: baseLimits.hasTripChat,
        // Additional perk info
        perkEnhancements: {
          additionalSquads: perkSummary.additionalSquads,
          additionalTrips: perkSummary.additionalTrips,
          additionalDailyAI: perkSummary.additionalDailyAI,
          additionalWeeklyAI: perkSummary.additionalWeeklyAI,
          activePerkIds: perkSummary.activePerkIds,
        },
      }

      return {
        isSubscribed,
        subscription,
        baseLimits,
        enhancedLimits,
        perkSummary,
      }
    } catch (error) {
      console.error("Error getting enhanced user limits:", error)
      
      // Return safe defaults on error
      return {
        isSubscribed: false,
        subscription: null,
        baseLimits: {
          maxSquads: SUBSCRIPTION_LIMITS.FREE.MAX_SQUADS,
          maxTripsPerSquad: SUBSCRIPTION_LIMITS.FREE.MAX_TRIPS_PER_SQUAD,
          maxDailyAIRequests: AI_USAGE_LIMITS.FREE.DAILY,
          maxWeeklyAIRequests: AI_USAGE_LIMITS.FREE.WEEKLY,
          hasTripChat: SUBSCRIPTION_LIMITS.FREE.HAS_TRIP_CHAT,
        },
        enhancedLimits: {
          maxSquads: SUBSCRIPTION_LIMITS.FREE.MAX_SQUADS,
          maxTripsPerSquad: SUBSCRIPTION_LIMITS.FREE.MAX_TRIPS_PER_SQUAD,
          maxDailyAIRequests: AI_USAGE_LIMITS.FREE.DAILY,
          maxWeeklyAIRequests: AI_USAGE_LIMITS.FREE.WEEKLY,
          hasTripChat: SUBSCRIPTION_LIMITS.FREE.HAS_TRIP_CHAT,
          perkEnhancements: {
            additionalSquads: 0,
            additionalTrips: 0,
            additionalDailyAI: 0,
            additionalWeeklyAI: 0,
            activePerkIds: [],
          },
        },
        perkSummary: {
          additionalSquads: 0,
          additionalTrips: 0,
          additionalDailyAI: 0,
          additionalWeeklyAI: 0,
          subscriptionDaysAdded: 0,
          activePerkIds: [],
        },
      }
    }
  }

  /**
   * Check if user can create more squads (perk-aware)
   */
  static async canCreateMoreSquads(userId: string, currentSquadCount?: number): Promise<boolean> {
    try {
      const { enhancedLimits } = await this.getEnhancedUserLimits(userId)
      
      // If we have infinite squads, return true
      if (enhancedLimits.maxSquads === Infinity) return true

      // If we already have the count, use it
      if (typeof currentSquadCount === "number") {
        return currentSquadCount < enhancedLimits.maxSquads
      }

      // Otherwise, fetch the current count
      const { SquadService } = await import("../squad/squad.service")
      const userSquads = await SquadService.getUserSquads(userId)
      return userSquads.length < enhancedLimits.maxSquads
    } catch (error) {
      console.error("Error checking if user can create more squads:", error)
      return false
    }
  }

  /**
   * Check if user can create more trips in a squad (perk-aware)
   */
  static async canCreateMoreTripsInSquad(
    userId: string,
    squadId: string,
    currentTripCount?: number
  ): Promise<boolean> {
    try {
      const { enhancedLimits } = await this.getEnhancedUserLimits(userId)
      
      // If we have infinite trips, return true
      if (enhancedLimits.maxTripsPerSquad === Infinity) return true

      // If we already have the count, use it
      if (typeof currentTripCount === "number") {
        return currentTripCount < enhancedLimits.maxTripsPerSquad
      }

      // Otherwise, fetch the current count
      const { TripService } = await import("../trip/trip.service")
      const squadTrips = await TripService.getSquadTrips(squadId)

      // Only count non-completed trips (planning, upcoming, active)
      const activeTrips = squadTrips.filter(
        (trip) =>
          trip.status === "planning" || trip.status === "upcoming" || trip.status === "active"
      )

      return activeTrips.length < enhancedLimits.maxTripsPerSquad
    } catch (error) {
      console.error("Error checking if user can create more trips in squad:", error)
      return false
    }
  }

  /**
   * Get enhanced AI usage limits (perk-aware)
   */
  static async getEnhancedAILimits(userId: string) {
    try {
      const { enhancedLimits } = await this.getEnhancedUserLimits(userId)
      
      return {
        dailyLimit: enhancedLimits.maxDailyAIRequests,
        weeklyLimit: enhancedLimits.maxWeeklyAIRequests,
        perkEnhancements: enhancedLimits.perkEnhancements,
      }
    } catch (error) {
      console.error("Error getting enhanced AI limits:", error)
      return {
        dailyLimit: AI_USAGE_LIMITS.FREE.DAILY,
        weeklyLimit: AI_USAGE_LIMITS.FREE.WEEKLY,
        perkEnhancements: {
          additionalDailyAI: 0,
          additionalWeeklyAI: 0,
          activePerkIds: [],
        },
      }
    }
  }

  /**
   * Apply subscription perks on login
   */
  static async applySubscriptionPerksOnLogin(userId: string): Promise<void> {
    try {
      // Get user's unlocked subscription perks that haven't been applied
      const userPerks = await PerkService.getUserPerks(userId)
      const unlockedSubscriptionPerks = userPerks.filter(
        perk => 
          perk.status === "unlocked" && 
          perk.perkDetails.perkType === "subscription" &&
          perk.perkDetails.tags.includes("subscription")
      )

      // Apply each unlocked subscription perk
      for (const perk of unlockedSubscriptionPerks) {
        try {
          const result = await PerkService.applySubscriptionPerk(userId, perk.perkId)
          if (result.success) {
            console.log(`Applied subscription perk ${perk.perkId} for user ${userId}`)
            
            // TODO: Add the subscription duration to user's subscription
            // This would require integration with the subscription service
            // to extend the current subscription period
          }
        } catch (error) {
          console.error(`Error applying subscription perk ${perk.perkId}:`, error)
        }
      }
    } catch (error) {
      console.error("Error applying subscription perks on login:", error)
    }
  }
}
