"use client"

import { create } from "zustand"
import { persist, createJSONStorage } from "zustand/middleware"
import {
  UserSubscription,
  SUBSCRIPTION_LIMITS,
  SubscriptionErrorType,
} from "./user-subscription.types"
import { UserSubscriptionService } from "./user-subscription.service"
import { PerkAwareSubscriptionService } from "./perk-aware-subscription.service"
import { handleSubscriptionError } from "./user-subscription.errors"
import { AI_USAGE_LIMITS } from "@/lib/firebase/ai-usage-service"
import { toast } from "@/components/ui/use-toast"

/**
 * User subscription store state interface
 */
interface UserSubscriptionState {
  // State
  subscription: UserSubscription | null
  loading: boolean
  error: Error | null
  isSubscribed: boolean
  subscriptionPlan: "free" | "monthly" | "yearly" | null
  subscriptionStatus: "active" | "canceled" | "past_due" | "trialing" | "incomplete" | null
  lastAuthRefresh: number

  // Limits (enhanced with perks)
  maxSquads: number
  maxTripsPerSquad: number
  maxDailyAIRequests: number
  maxWeeklyAIRequests: number
  hasTripChat: boolean

  // Perk enhancements
  perkEnhancements: {
    additionalSquads: number
    additionalTrips: number
    additionalDailyAI: number
    additionalWeeklyAI: number
    activePerkIds: string[]
  } | null

  // Actions
  setSubscription: (subscription: UserSubscription | null) => void
  setLoading: (loading: boolean) => void
  fetchSubscription: (userId: string) => Promise<void>
  fetchEnhancedLimits: (userId: string) => Promise<void>
  refreshSubscriptionIfNeeded: (userId: string) => void
  canCreateMoreSquads: (userId: string, currentSquadCount?: number) => Promise<boolean>
  canCreateMoreTripsInSquad: (
    userId: string,
    squadId: string,
    currentTripCount?: number
  ) => Promise<boolean>
  handleSubscriptionError: (errorType: SubscriptionErrorType) => void
  handleSubscriptionErrorWithRouter: (errorType: SubscriptionErrorType, router: any) => void
}

// Create a custom storage object that only uses localStorage on the client side
const customStorage = {
  getItem: (name: string) => {
    if (typeof window === "undefined") return null
    return window.localStorage.getItem(name)
  },
  setItem: (name: string, value: string) => {
    if (typeof window !== "undefined") {
      window.localStorage.setItem(name, value)
    }
  },
  removeItem: (name: string) => {
    if (typeof window !== "undefined") {
      window.localStorage.removeItem(name)
    }
  },
}

/**
 * User subscription store with Zustand
 */
export const useUserSubscriptionStore = create<UserSubscriptionState>()(
  persist(
    (set, get) => ({
      // Initial state
      subscription: null,
      loading: true,
      error: null,
      isSubscribed: false,
      subscriptionPlan: "free",
      subscriptionStatus: null,
      lastAuthRefresh: 0,

      // Default limits
      maxSquads: SUBSCRIPTION_LIMITS.FREE.MAX_SQUADS,
      maxTripsPerSquad: SUBSCRIPTION_LIMITS.FREE.MAX_TRIPS_PER_SQUAD,
      maxDailyAIRequests: AI_USAGE_LIMITS.FREE.DAILY,
      maxWeeklyAIRequests: AI_USAGE_LIMITS.FREE.WEEKLY,
      hasTripChat: SUBSCRIPTION_LIMITS.FREE.HAS_TRIP_CHAT,

      // Default perk enhancements
      perkEnhancements: null,

      // Actions
      setSubscription: (subscription) => {
        const isSubscribed =
          subscription?.subscriptionStatus === "active" ||
          subscription?.subscriptionStatus === "trialing"

        // Determine limits based on subscription status
        const maxSquads = isSubscribed
          ? SUBSCRIPTION_LIMITS.PRO.MAX_SQUADS
          : SUBSCRIPTION_LIMITS.FREE.MAX_SQUADS

        const maxTripsPerSquad = isSubscribed
          ? SUBSCRIPTION_LIMITS.PRO.MAX_TRIPS_PER_SQUAD
          : SUBSCRIPTION_LIMITS.FREE.MAX_TRIPS_PER_SQUAD

        const maxDailyAIRequests = isSubscribed
          ? AI_USAGE_LIMITS.PRO.DAILY
          : AI_USAGE_LIMITS.FREE.DAILY

        const maxWeeklyAIRequests = isSubscribed
          ? AI_USAGE_LIMITS.PRO.WEEKLY
          : AI_USAGE_LIMITS.FREE.WEEKLY

        const hasTripChat = isSubscribed
          ? SUBSCRIPTION_LIMITS.PRO.HAS_TRIP_CHAT
          : SUBSCRIPTION_LIMITS.FREE.HAS_TRIP_CHAT

        set({
          subscription,
          isSubscribed,
          subscriptionPlan: subscription?.subscriptionPlan || "free",
          subscriptionStatus: subscription?.subscriptionStatus || null,
          maxSquads,
          maxTripsPerSquad,
          maxDailyAIRequests,
          maxWeeklyAIRequests,
          hasTripChat,
        })
      },

      setLoading: (loading) => set({ loading }),

      // Fetch subscription data
      fetchSubscription: async (userId: string) => {
        if (!userId) {
          set({
            loading: false,
            subscription: null,
            isSubscribed: false,
            subscriptionPlan: "free",
            subscriptionStatus: null,
            maxSquads: SUBSCRIPTION_LIMITS.FREE.MAX_SQUADS,
            maxTripsPerSquad: SUBSCRIPTION_LIMITS.FREE.MAX_TRIPS_PER_SQUAD,
            maxDailyAIRequests: AI_USAGE_LIMITS.FREE.DAILY,
            maxWeeklyAIRequests: AI_USAGE_LIMITS.FREE.WEEKLY,
            hasTripChat: SUBSCRIPTION_LIMITS.FREE.HAS_TRIP_CHAT,
            lastAuthRefresh: 0,
          })
          return
        }

        try {
          set({ loading: true, error: null })
          const subscription = await UserSubscriptionService.getUserSubscription(userId)

          // Update the last refresh timestamp
          set({ lastAuthRefresh: Date.now() })

          // Set the subscription data
          get().setSubscription(subscription)
          set({ loading: false })
        } catch (error) {
          console.error("Error fetching subscription:", error)
          set({
            error: error as Error,
            loading: false,
            subscription: null,
            isSubscribed: false,
            subscriptionPlan: "free",
            subscriptionStatus: null,
          })
        }
      },

      // Fetch enhanced limits with perks
      fetchEnhancedLimits: async (userId: string) => {
        if (!userId) {
          set({
            loading: false,
            perkEnhancements: null,
          })
          return
        }

        try {
          set({ loading: true, error: null })

          const enhancedData = await PerkAwareSubscriptionService.getEnhancedUserLimits(userId)

          set({
            subscription: enhancedData.subscription,
            isSubscribed: enhancedData.isSubscribed,
            subscriptionPlan: enhancedData.subscription?.subscriptionPlan || "free",
            subscriptionStatus: enhancedData.subscription?.subscriptionStatus || null,
            maxSquads: enhancedData.enhancedLimits.maxSquads,
            maxTripsPerSquad: enhancedData.enhancedLimits.maxTripsPerSquad,
            maxDailyAIRequests: enhancedData.enhancedLimits.maxDailyAIRequests,
            maxWeeklyAIRequests: enhancedData.enhancedLimits.maxWeeklyAIRequests,
            hasTripChat: enhancedData.enhancedLimits.hasTripChat,
            perkEnhancements: enhancedData.enhancedLimits.perkEnhancements,
            loading: false,
          })
        } catch (error) {
          console.error("Error fetching enhanced limits:", error)
          set({
            error: error as Error,
            loading: false,
            perkEnhancements: null,
          })
        }
      },

      // Refresh subscription if needed (e.g., after a long session)
      refreshSubscriptionIfNeeded: (userId: string) => {
        const { lastAuthRefresh } = get()
        const now = Date.now()
        const refreshInterval = 30 * 60 * 1000 // 30 minutes

        // Refresh if it's been more than the refresh interval
        if (now - lastAuthRefresh > refreshInterval) {
          get().fetchSubscription(userId)
        }
      },

      // Check if a user can create more squads (perk-aware)
      canCreateMoreSquads: async (userId: string, currentSquadCount?: number) => {
        try {
          return await PerkAwareSubscriptionService.canCreateMoreSquads(userId, currentSquadCount)
        } catch (error) {
          console.error("Error checking if user can create more squads:", error)
          return false
        }
      },

      // Check if a user can create more trips in a squad (perk-aware)
      canCreateMoreTripsInSquad: async (
        userId: string,
        squadId: string,
        currentTripCount?: number
      ) => {
        try {
          return await PerkAwareSubscriptionService.canCreateMoreTripsInSquad(
            userId,
            squadId,
            currentTripCount
          )
        } catch (error) {
          console.error("Error checking if user can create more trips in squad:", error)
          return false
        }
      },

      // Handle subscription errors
      handleSubscriptionError: (errorType: SubscriptionErrorType) => {
        const { isSubscribed } = get()
        handleSubscriptionError(errorType, isSubscribed)
      },

      // Handle subscription errors with router navigation
      handleSubscriptionErrorWithRouter: (errorType: SubscriptionErrorType, router: any) => {
        const { isSubscribed } = get()
        handleSubscriptionError(errorType, isSubscribed, router)
      },
    }),
    {
      name: "brotrips-user-subscription-storage",
      storage: createJSONStorage(() => customStorage),
      skipHydration: true, // Skip hydration to prevent hydration mismatch
      partialize: (state) => ({
        // Only persist these fields
        subscriptionPlan: state.subscriptionPlan,
        subscriptionStatus: state.subscriptionStatus,
      }),
    }
  )
)
