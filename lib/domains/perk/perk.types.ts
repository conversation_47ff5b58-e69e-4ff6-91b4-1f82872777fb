import { Timestamp } from "firebase/firestore"
import { BaseEntity } from "../base/base.types"

/**
 * Perk type enumeration
 */
export type PerkType = "subscription" | "squad" | "trip" | "ai_usage"

/**
 * Perk tag enumeration for feature integration
 */
export type PerkTag = "trip" | "squad" | "subscription" | "permanent"

/**
 * Perk status enumeration
 */
export type PerkStatus = "unlocked" | "applied" | "expired"

/**
 * Perk value interface for different perk types
 */
export interface PerkValue {
  // For subscription perks
  duration?: number // Duration in days
  unit?: "days" | "months" | "years"
  
  // For squad perks
  maxSquads?: number // Additional squads allowed
  
  // For trip perks
  maxTrips?: number // Additional trips allowed
  
  // For AI usage perks
  dailyRequests?: number // Additional daily AI requests
  weeklyRequests?: number // Additional weekly AI requests
}

/**
 * Global perk definition stored at referral/perks/{perkId}
 */
export interface GlobalPerk extends BaseEntity {
  name: string // Display name of the perk
  description: string // Description of what the perk provides
  referralCount: number // Number of referrals required to unlock
  perkType: PerkType // Type of perk (subscription, squad, etc.)
  perkValue: PerkValue // Value/benefits of the perk
  tags: PerkTag[] // Tags for feature integration
  isActive: boolean // Whether the perk is currently active
}

/**
 * User perk instance stored at users/{userId}/perks/{perkId}
 */
export interface UserPerk extends BaseEntity {
  perkId: string // Reference to global perk definition
  unlockedAt: Timestamp | null // When the perk was unlocked
  appliedAt: Timestamp | null // When the perk was applied (for subscription perks)
  expiresAt: Timestamp | null // When the perk expires (null for permanent perks)
  status: PerkStatus // Current status of the perk
  perkDetails: GlobalPerk // Copied global perk definition for historical reference
}

/**
 * Global perk creation data
 */
export type GlobalPerkCreateData = Omit<GlobalPerk, "id" | "createdAt" | "updatedAt">

/**
 * Global perk update data
 */
export type GlobalPerkUpdateData = Partial<Omit<GlobalPerk, "id" | "createdAt">>

/**
 * User perk creation data
 */
export type UserPerkCreateData = Omit<UserPerk, "id" | "createdAt" | "updatedAt">

/**
 * User perk update data
 */
export type UserPerkUpdateData = Partial<Omit<UserPerk, "id" | "perkId" | "createdAt">>

/**
 * Perk application result
 */
export interface PerkApplicationResult {
  success: boolean
  perkId?: string
  appliedAt?: Timestamp
  error?: string
}

/**
 * User perk summary for calculating limits
 */
export interface UserPerkSummary {
  additionalSquads: number
  additionalTrips: number
  additionalDailyAI: number
  additionalWeeklyAI: number
  subscriptionDaysAdded: number
  activePerkIds: string[]
}

/**
 * Perk eligibility check result
 */
export interface PerkEligibilityResult {
  isEligible: boolean
  perkId: string
  currentReferrals: number
  requiredReferrals: number
  alreadyUnlocked: boolean
}

/**
 * Default global perk definitions
 */
export const DEFAULT_GLOBAL_PERKS: Omit<GlobalPerk, "id" | "createdAt" | "updatedAt">[] = [
  {
    name: "1 Free Squad",
    description: "Unlock 1 additional squad slot permanently",
    referralCount: 5,
    perkType: "squad",
    perkValue: {
      maxSquads: 1,
    },
    tags: ["squad", "permanent"],
    isActive: true,
  },
  {
    name: "2 Months Free Pro",
    description: "Get 2 months of Pro subscription for free",
    referralCount: 10,
    perkType: "subscription",
    perkValue: {
      duration: 60,
      unit: "days",
    },
    tags: ["subscription"],
    isActive: true,
  },
]
