"use client"

import { useEffect } from "react"
import { useUser } from "../auth/auth.hooks"
import { useReferralStore } from "./referral.store"

/**
 * Basic referral store selectors
 */
export const useReferralCode = () => useReferralStore((state) => state.userReferralCode)
export const useUserReferrals = () => useReferralStore((state) => state.userReferrals)
export const useReferralLoading = () => useReferralStore((state) => state.loading)
export const useReferralError = () => useReferralStore((state) => state.error)

/**
 * Referral action selectors
 */
export const useFetchUserReferralCode = () =>
  useReferralStore((state) => state.fetchUserReferralCode)
export const useFetchUserReferrals = () => useReferralStore((state) => state.fetchUserReferrals)
export const useGenerateAndCreateReferralCode = () =>
  useReferralStore((state) => state.generateAndCreateReferralCode)
export const useValidateReferralCode = () => useReferralStore((state) => state.validateReferralCode)
export const useProcessReferral = () => useReferralStore((state) => state.processReferral)

/**
 * Computed value selectors
 */
export const useGetTotalReferrals = () => useReferralStore((state) => state.getTotalReferrals)
export const useGetReferralProgress = () => useReferralStore((state) => state.getReferralProgress)

/**
 * Hook to get referral code with auto-initialization
 */
export const useReferralCodeWithInit = () => {
  const user = useUser()
  const referralCode = useReferralCode()
  const loading = useReferralLoading()
  const fetchUserReferralCode = useFetchUserReferralCode()
  const generateAndCreateReferralCode = useGenerateAndCreateReferralCode()

  // Fetch referral code when user changes
  useEffect(() => {
    if (user?.uid && !referralCode && !loading) {
      fetchUserReferralCode(user.uid)
    }
  }, [user, referralCode, loading, fetchUserReferralCode])

  // Generate referral code if user doesn't have one
  const ensureReferralCode = async () => {
    if (!user?.uid) return null
    
    if (referralCode) {
      return referralCode.id
    }

    return await generateAndCreateReferralCode(user.uid)
  }

  return {
    referralCode,
    loading,
    ensureReferralCode,
  }
}

/**
 * Hook to get user referrals with auto-initialization
 */
export const useUserReferralsWithInit = () => {
  const user = useUser()
  const referrals = useUserReferrals()
  const loading = useReferralLoading()
  const fetchUserReferrals = useFetchUserReferrals()

  // Fetch referrals when user changes
  useEffect(() => {
    if (user?.uid) {
      fetchUserReferrals(user.uid)
    }
  }, [user, fetchUserReferrals])

  return {
    referrals,
    loading,
  }
}

/**
 * Hook to get referral progress for specific goals
 */
export const useReferralGoals = () => {
  const getTotalReferrals = useGetTotalReferrals()
  const getReferralProgress = useGetReferralProgress()

  const totalReferrals = getTotalReferrals()

  // Define referral goals based on the user story
  const goals = [
    {
      id: "squad_perk",
      name: "1 free squad",
      description: "5 referrals = 1 free squad",
      targetReferrals: 5,
      perkType: "squad",
      progress: getReferralProgress(5),
      isCompleted: totalReferrals >= 5,
    },
    {
      id: "subscription_perk",
      name: "2 months free",
      description: "10 referrals = 2 months free (2 month pro subscription)",
      targetReferrals: 10,
      perkType: "subscription",
      progress: getReferralProgress(10),
      isCompleted: totalReferrals >= 10,
    },
  ]

  return {
    goals,
    totalReferrals,
  }
}

/**
 * Hook to handle referral code sharing
 */
export const useReferralSharing = () => {
  const referralCode = useReferralCode()

  const getReferralLink = () => {
    if (!referralCode) return null
    return `${window.location.origin}/r/${referralCode.id}`
  }

  const getSignupLink = () => {
    if (!referralCode) return null
    return `${window.location.origin}/signup?referral_code=${referralCode.id}`
  }

  const copyReferralLink = async () => {
    const link = getReferralLink()
    if (!link) return false

    try {
      await navigator.clipboard.writeText(link)
      return true
    } catch (error) {
      console.error("Failed to copy referral link:", error)
      return false
    }
  }

  const copyReferralCode = async () => {
    if (!referralCode) return false

    try {
      await navigator.clipboard.writeText(referralCode.id)
      return true
    } catch (error) {
      console.error("Failed to copy referral code:", error)
      return false
    }
  }

  return {
    referralCode: referralCode?.id || null,
    referralLink: getReferralLink(),
    signupLink: getSignupLink(),
    copyReferralLink,
    copyReferralCode,
  }
}
