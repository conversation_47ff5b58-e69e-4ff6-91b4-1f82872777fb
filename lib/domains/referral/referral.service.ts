import { db } from "@/lib/firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  setDoc,
  updateDoc,
  query,
  where,
  serverTimestamp,
  runTransaction,
  writeBatch,
} from "firebase/firestore"
import { BaseService } from "../base/base.service"
import { ServiceResponse } from "../base/base.types"
import {
  ReferralCode,
  ReferralCodeCreateData,
  ReferralCodeUpdateData,
  UserReferral,
  UserReferralCreateData,
  ReferralCodeGenerationOptions,
  ReferralProcessingResult,
  ReferralValidationResult,
} from "./referral.types"

/**
 * Referral service for Firebase operations
 */
export class ReferralService extends BaseService {
  private static readonly REFERRAL_COLLECTION = "referral"
  private static readonly USER_REFERRALS_SUBCOLLECTION = "referrals"

  /**
   * Generate a unique 8-character alphanumeric referral code
   */
  static async generateReferralCode(options: ReferralCodeGenerationOptions = {}): Promise<string> {
    const { length = 8, maxRetries = 10 } = options
    const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

    for (let attempt = 0; attempt < maxRetries; attempt++) {
      let code = ""
      for (let i = 0; i < length; i++) {
        code += characters.charAt(Math.floor(Math.random() * characters.length))
      }

      // Check if code already exists
      const exists = await this.referralCodeExists(code)
      if (!exists) {
        return code
      }
    }

    throw new Error(`Failed to generate unique referral code after ${maxRetries} attempts`)
  }

  /**
   * Check if a referral code exists
   */
  static async referralCodeExists(code: string): Promise<boolean> {
    try {
      const docRef = doc(db, this.REFERRAL_COLLECTION, code)
      const docSnap = await getDoc(docRef)
      return docSnap.exists()
    } catch (error) {
      console.error("Error checking referral code existence:", error)
      return false
    }
  }

  /**
   * Create a referral code for a user
   */
  static async createReferralCode(
    code: string,
    userId: string
  ): Promise<ServiceResponse<ReferralCode>> {
    try {
      const referralData: ReferralCodeCreateData = {
        userId,
        totalReferrals: 0,
        isActive: true,
      }

      await setDoc(doc(db, this.REFERRAL_COLLECTION, code), {
        ...referralData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      })

      return {
        success: true,
        id: code,
        data: {
          id: code,
          ...referralData,
          createdAt: null, // Will be set by server
          updatedAt: null,
        } as ReferralCode,
      }
    } catch (error) {
      console.error("Error creating referral code:", error)
      return { success: false, error }
    }
  }

  /**
   * Get referral code by code string
   */
  static async getReferralCode(code: string): Promise<ReferralCode | null> {
    try {
      const docRef = doc(db, this.REFERRAL_COLLECTION, code)
      const docSnap = await getDoc(docRef)

      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() } as ReferralCode
      }

      return null
    } catch (error) {
      console.error("Error getting referral code:", error)
      return null
    }
  }

  /**
   * Get referral code by user ID
   */
  static async getReferralCodeByUserId(userId: string): Promise<ReferralCode | null> {
    try {
      const q = query(collection(db, this.REFERRAL_COLLECTION), where("userId", "==", userId))
      const querySnapshot = await getDocs(q)

      if (!querySnapshot.empty) {
        const doc = querySnapshot.docs[0]
        return { id: doc.id, ...doc.data() } as ReferralCode
      }

      return null
    } catch (error) {
      console.error("Error getting referral code by user ID:", error)
      return null
    }
  }

  /**
   * Validate a referral code
   */
  static async validateReferralCode(code: string): Promise<ReferralValidationResult> {
    try {
      const referralCode = await this.getReferralCode(code)

      if (!referralCode) {
        return {
          isValid: false,
          exists: false,
          isActive: false,
          error: "Referral code does not exist",
        }
      }

      if (!referralCode.isActive) {
        return {
          isValid: false,
          exists: true,
          isActive: false,
          ownerId: referralCode.userId,
          error: "Referral code is not active",
        }
      }

      return {
        isValid: true,
        exists: true,
        isActive: true,
        ownerId: referralCode.userId,
      }
    } catch (error) {
      console.error("Error validating referral code:", error)
      return {
        isValid: false,
        exists: false,
        isActive: false,
        error: "Error validating referral code",
      }
    }
  }

  /**
   * Update referral code
   */
  static async updateReferralCode(
    code: string,
    updateData: ReferralCodeUpdateData
  ): Promise<ServiceResponse> {
    try {
      const docRef = doc(db, this.REFERRAL_COLLECTION, code)
      await updateDoc(docRef, {
        ...updateData,
        updatedAt: serverTimestamp(),
      })

      return { success: true }
    } catch (error) {
      console.error("Error updating referral code:", error)
      return { success: false, error }
    }
  }

  /**
   * Get user's referrals
   */
  static async getUserReferrals(userId: string): Promise<UserReferral[]> {
    try {
      const referralsRef = collection(db, "users", userId, this.USER_REFERRALS_SUBCOLLECTION)
      const querySnapshot = await getDocs(referralsRef)

      return querySnapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }) as UserReferral)
    } catch (error) {
      console.error("Error getting user referrals:", error)
      return []
    }
  }

  /**
   * Create a user referral record
   */
  static async createUserReferral(
    userId: string,
    referralData: UserReferralCreateData
  ): Promise<ServiceResponse<UserReferral>> {
    try {
      const referralRef = doc(collection(db, "users", userId, this.USER_REFERRALS_SUBCOLLECTION))

      await setDoc(referralRef, {
        ...referralData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      })

      return {
        success: true,
        id: referralRef.id,
        data: {
          id: referralRef.id,
          ...referralData,
          createdAt: null, // Will be set by server
          updatedAt: null,
        } as UserReferral,
      }
    } catch (error) {
      console.error("Error creating user referral:", error)
      return { success: false, error }
    }
  }

  /**
   * Process a referral (increment count and create referral record)
   */
  static async processReferral(
    referralCode: string,
    referredUserId: string,
    referredUserEmail: string
  ): Promise<ReferralProcessingResult> {
    try {
      // Validate the referral code first
      const validation = await this.validateReferralCode(referralCode)
      if (!validation.isValid || !validation.ownerId) {
        return {
          success: false,
          error: validation.error || "Invalid referral code",
        }
      }

      const referrerId = validation.ownerId

      // Prevent self-referral
      if (referrerId === referredUserId) {
        return {
          success: false,
          error: "Cannot refer yourself",
        }
      }

      // Use a transaction to ensure consistency
      const result = await runTransaction(db, async (transaction) => {
        // Get current referral code data
        const referralCodeRef = doc(db, this.REFERRAL_COLLECTION, referralCode)
        const referralCodeDoc = await transaction.get(referralCodeRef)

        if (!referralCodeDoc.exists()) {
          throw new Error("Referral code not found")
        }

        const currentData = referralCodeDoc.data() as ReferralCode
        const newTotalReferrals = currentData.totalReferrals + 1

        // Update referral code with incremented count
        transaction.update(referralCodeRef, {
          totalReferrals: newTotalReferrals,
          updatedAt: serverTimestamp(),
        })

        // Create referral record for the referrer
        const userReferralRef = doc(
          collection(db, "users", referrerId, this.USER_REFERRALS_SUBCOLLECTION)
        )

        const referralData: UserReferralCreateData = {
          referredUserId,
          referredUserEmail,
          referralCode,
          status: "completed",
        }

        transaction.set(userReferralRef, {
          ...referralData,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        })

        return {
          referralId: userReferralRef.id,
          newTotalReferrals,
        }
      })

      // Check for newly unlocked perks based on newTotalReferrals
      let perksUnlocked: string[] = []
      try {
        const { PerkService } = await import("../perk/perk.service")
        perksUnlocked = await PerkService.unlockEligiblePerks(referrerId, result.newTotalReferrals)
      } catch (error) {
        console.error("Error checking for unlocked perks:", error)
        // Don't fail the referral process if perk checking fails
      }

      return {
        success: true,
        referralId: result.referralId,
        perksUnlocked,
      }
    } catch (error) {
      console.error("Error processing referral:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      }
    }
  }
}
