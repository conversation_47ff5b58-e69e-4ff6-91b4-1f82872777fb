"use client"

import { useState, useRef, useEffect, useCallback } from "react"
import { useSearchParams, useRouter, usePathname } from "next/navigation"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"

// Import the component files
import { ProfileSettings } from "./profile-settings"
import { TravelPreferences } from "./travel-preferences"
import { ThemeSettings } from "./theme-settings"
import { AppPreferences } from "./app-preferences"
import { AIPreferences } from "./ai-preferences"
import { NotificationSettings } from "./notification-settings"
import { PrivacySettings } from "./privacy-settings"
import { BillingSettingsWrapper } from "./billing-settings-wrapper"
import { ActivityPreferencesSettings } from "./activity-preferences-settings"
import { ReferralsSettings } from "./referrals-settings"
import { useIsUserSubscribed } from "@/lib/domains/user-subscription"

export function SettingsContent() {
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const [activeTab, setActiveTab] = useState("profile")
  const tabsListRef = useRef<HTMLDivElement>(null)
  const isSubscribed = useIsUserSubscribed()

  // Function to update the URL when tab changes
  const handleTabChange = useCallback(
    (value: string) => {
      setActiveTab(value)

      // Create a new URLSearchParams object
      const params = new URLSearchParams(searchParams.toString())
      params.set("tab", value)

      // Update the URL without refreshing the page
      router.replace(`${pathname}?${params.toString()}`, { scroll: false })
    },
    [pathname, router, searchParams]
  )

  // Handle URL parameters for tab selection
  useEffect(() => {
    const tabParam = searchParams.get("tab")
    if (
      tabParam &&
      [
        "profile",
        "preferences",
        "notifications",
        "privacy",
        "billing",
        "activity-preferences",
      ].includes(tabParam)
    ) {
      setActiveTab(tabParam)

      // Scroll the tab into view on mobile
      setTimeout(() => {
        if (tabsListRef.current) {
          const tabElement = tabsListRef.current.querySelector(`[data-value="${tabParam}"]`)
          if (tabElement) {
            // Scroll the tab into view with some padding
            const container = tabsListRef.current
            const scrollLeft =
              tabElement.getBoundingClientRect().left -
              container.getBoundingClientRect().left +
              container.scrollLeft -
              16 // Add some padding

            container.scrollTo({
              left: scrollLeft,
              behavior: "smooth",
            })
          }
        }
      }, 100) // Small delay to ensure the DOM is updated
    }
  }, [searchParams])

  return (
    <Tabs
      defaultValue="profile"
      className="space-y-4 w-full"
      onValueChange={handleTabChange}
      value={activeTab}
    >
      <div className="overflow-x-auto pb-2 max-w-[100vw] no-scrollbar">
        <TabsList className="w-full flex flex-nowrap overflow-x-auto" ref={tabsListRef}>
          <TabsTrigger value="profile" className="flex-shrink-0">
            Profile
          </TabsTrigger>
          <TabsTrigger value="preferences" className="flex-shrink-0">
            Preferences
          </TabsTrigger>
          {isSubscribed && (
            <TabsTrigger value="activity-preferences" className="flex-shrink-0">
              Activity Preferences
            </TabsTrigger>
          )}
          <TabsTrigger value="notifications" className="flex-shrink-0">
            Notifications
          </TabsTrigger>
          <TabsTrigger value="privacy" className="flex-shrink-0">
            Privacy & Security
          </TabsTrigger>
          <TabsTrigger value="billing" className="flex-shrink-0">
            Billing
          </TabsTrigger>
          <TabsTrigger value="referrals" className="flex-shrink-0">
            Referrals
          </TabsTrigger>
        </TabsList>
      </div>

      <TabsContent value="profile" className="space-y-4 w-full max-w-full overflow-x-hidden">
        <ProfileSettings />
        <TravelPreferences />
      </TabsContent>

      <TabsContent value="preferences" className="space-y-4 w-full max-w-full overflow-x-hidden">
        <ThemeSettings />
        <AppPreferences />
        <AIPreferences />
      </TabsContent>

      {isSubscribed && (
        <TabsContent
          value="activity-preferences"
          className="space-y-4 w-full max-w-full overflow-x-hidden"
        >
          <ActivityPreferencesSettings />
        </TabsContent>
      )}

      <TabsContent value="notifications" className="space-y-4 w-full max-w-full overflow-x-hidden">
        <NotificationSettings />
      </TabsContent>

      <TabsContent value="privacy" className="space-y-4 w-full max-w-full overflow-x-hidden">
        <PrivacySettings />
      </TabsContent>

      <TabsContent value="billing" className="space-y-4 w-full max-w-full overflow-x-hidden">
        <BillingSettingsWrapper />
      </TabsContent>

      <TabsContent value="referrals" className="space-y-4 w-full max-w-full overflow-x-hidden">
        <ReferralsSettings />
      </TabsContent>
    </Tabs>
  )
}
