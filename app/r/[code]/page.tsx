"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Logo } from "@/components/ui/logo"
import { Loader2 } from "lucide-react"

interface ReferralRedirectPageProps {
  params: {
    code: string
  }
}

/**
 * Referral short link redirect page
 * Redirects /r/[code] to /signup?referral_code=[code]
 */
export default function ReferralRedirectPage({ params }: ReferralRedirectPageProps) {
  const router = useRouter()
  const { code } = params

  useEffect(() => {
    // Validate the referral code format (8 alphanumeric characters)
    const isValidFormat = /^[A-Z0-9]{8}$/.test(code.toUpperCase())
    
    if (isValidFormat) {
      // Redirect to signup with referral code parameter
      router.replace(`/signup?referral_code=${code.toUpperCase()}`)
    } else {
      // Invalid format, redirect to signup without referral code
      router.replace("/signup")
    }
  }, [code, router])

  return (
    <div className="min-h-screen bg-gradient-to-br from-teal-50 to-yellow-50 flex flex-col">
      {/* Header */}
      <header className="p-4">
        <Logo />
      </header>

      {/* Main content */}
      <main className="flex-1 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center gap-2">
              <Loader2 className="h-5 w-5 animate-spin" />
              Processing Referral
            </CardTitle>
            <CardDescription>
              Redirecting you to sign up with referral code: {code.toUpperCase()}
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-sm text-muted-foreground">
              If you're not redirected automatically, 
              <a href={`/signup?referral_code=${code.toUpperCase()}`} className="text-primary hover:underline ml-1">
                click here
              </a>
            </p>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
